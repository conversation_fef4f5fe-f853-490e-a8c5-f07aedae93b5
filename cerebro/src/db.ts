// src/db.ts - versión corregida para mysql2
import mysql from 'mysql2/promise';
import { env } from './env.js';
import { logger } from './logger.js';

export const pool = mysql.createPool({
  host: env.DB_HOST,
  port: env.DB_PORT,
  user: env.DB_USER,
  password: env.DB_PASSWORD,
  database: env.DB_NAME,

  // ✅ Pool configuration correcta para mysql2
  connectionLimit: 10,
  queueLimit: 0,
  
  // ✅ Timeout configuration correcta
  acquireTimeout: 60000,
  timeout: 60000,
  
  // ✅ Character set
  charset: 'utf8mb4',
  
  // ✅ SSL corregido - solo si es necesario en prod
  ...(process.env.NODE_ENV === 'production' && {
    ssl: { rejectUnauthorized: false }
  }),
  
  // ✅ Timezone
  timezone: '+00:00', // UTC en formato correcto para mysql2
  
  // ✅ Otras opciones importantes
  multipleStatements: false,
  dateStrings: false,
  supportBigNumbers: true,
  bigNumberStrings: false,
});

// Health check del pool
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const [rows] = await pool.query('SELECT 1 as health');
    return Array.isArray(rows) && rows.length > 0;
  } catch (error) {
    logger.error({ error }, 'Database health check failed');
    return false;
  }
}

// Graceful shutdown
export async function closeDatabasePool(): Promise<void> {
  try {
    await pool.end();
    logger.info('Database pool closed gracefully');
  } catch (error) {
    logger.error({ error }, 'Error closing database pool');
  }
}

// ✅ Event listeners para debug (opcional)
pool.on('connection', (connection: any) => {
  logger.debug({ connectionId: connection.threadId }, 'New DB connection established');
});

// ✅ Para capturar errores del pool
pool.on('error', (error: any) => {
  logger.error({ error: error.message }, 'Database pool error');
});
