.
├── docker-compose.dev.yml
├── Dockerfile
├── _docs
│   └── file-system.md
├── .env
├── .env.example
├── .gitignore
├── Makefile
├── migrations
│   ├── 001_init.sql
│   └── 002_seed_minima.sql
├── package.json
├── package-lock.json
├── README.md
├── scripts
│   ├── gen-jwk.ts
│   ├── migrate.ts
│   └── seed.ts
├── src
│   ├── crypto
│   │   └── jwk.ts
│   ├── db.ts
│   ├── env.ts
│   ├── index.ts
│   ├── logger.ts
│   ├── middlewares
│   │   ├── rateLimit.ts
│   │   └── requireAdmin.ts
│   ├── routes
│   │   ├── admin.ts
│   │   ├── authorize.ts
│   │   ├── jwks.ts
│   │   ├── profile.ts
│   │   ├── token.ts
│   │   ├── userinfo.ts
│   │   └── wellKnown.ts
│   └── services
│       ├── oidc.ts
│       ├── tokens.ts
│       └── users.ts
└── tsconfig.json

9 directories, 33 files
